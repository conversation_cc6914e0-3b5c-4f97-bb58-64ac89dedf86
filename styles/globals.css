@import url('https://fonts.googleapis.com/css2?family=Satisfy&display=swap') layer(base);
@import 'katex/dist/katex.min.css' layer(base);

@import 'tailwindcss';

@config "../tailwind.config.ts";

/* Hide scrollbars globally while maintaining scroll functionality */
* {
	/* For WebKit browsers (Chrome, Safari) */
	::-webkit-scrollbar {
		display: none;
	}

	/* For Firefox */
	scrollbar-width: none;

	/* For Internet Explorer and Edge */
	-ms-overflow-style: none;
}

.katex-mathml {
	display: none !important;
}

.Satisfy {
	font-family: 'Satisfy', cursive;
	font-weight: normal;
	font-style: normal;
}

.text-fade-right {
	-webkit-mask-image: linear-gradient(to right, black 75%, transparent 95%);
	mask-image: linear-gradient(to right, black 75%, transparent 95%);
}
